import logging
import os

import numpy as np
import torch

from utils.augmentations import letterbox
from utils.general import check_img_size, non_max_suppression, scale_coords
from utils.torch_utils import select_device


class TPHAlgorithm:
    """YOLOv5 TPH 检测算法，集成跟踪功能"""
    alg_type = "yolov5"

    def __init__(self, alg_key: str, config: dict):
        self.logger = logging.getLogger(__name__)
        self.model = None
        self.names = {}
        self.stride = None
        self.imgsz = config.get("imgsz", [640, 640])
        self.device = config.get("device", "")
        self.half = config.get("half", True)
        self.conf = config.get("conf", 0.25)
        self.iou = config.get("iou", 0.45)
        self.max_det = config.get("max_det", 1000)
        self.weights = config.get("weights", f"models/{alg_key}.pt")


    def initialize(self) -> bool:
        """初始化算法"""
        try:
            # 获取配置参数
            weights_path = self.weights
            if not os.path.isfile(weights_path):
                self.logger.error(f"找不到模型文件: {weights_path}")
                raise FileNotFoundError(f"找不到模型文件: {weights_path}")

            self.logger.info(f"初始化YOLOv5 TPH模型: {weights_path}")

            # 初始化设备
            self.device = select_device(self.device)
            self.half &= self.device.type != "cpu"

            # 加载模型
            self.model = self._load_model(weights_path)
            self.stride = int(self.model.stride.max())
            self.names = (
                self.model.module.names
                if hasattr(self.model, "module")
                else self.model.names
            )
            self.names = dict(enumerate(self.names))
            self.imgsz = check_img_size(self.imgsz, s=self.stride)

            # 初始化跟踪器
            track_config = self.config.get("track", {})

            # 预热模型
            if self.device.type != "cpu":
                self.model(
                    torch.zeros(1, 3, *self.imgsz)
                    .to(self.device)
                    .type_as(next(self.model.parameters()))
                )

            self.logger.info(
                f"YOLOv5 TPH算法初始化成功:{self.alg_key};{weights_path};{self.imgsz};"
                f"{self.conf};{self.iou};{self.device};"
                f"{self.half};{self.max_det};{self.names}"
            )

            self._initialized = True
            return True

        except Exception as e:
            self.logger.error("YOLOv5 TPH算法初始化失败:", exc_info=True)
            raise RuntimeError(f"YOLOv5 TPH算法初始化失败: {e}")

    def _load_model(self, weights_path):
        """加载模型"""
        from .models.experimental import attempt_load

        w = str(weights_path)
        model = (
            torch.jit.load(w)
            if "torchscript" in w
            else attempt_load(weights_path, map_location=self.device)
        )
        if self.half:
            model.half()
        return model

    def process(self, frame):
        """处理帧并返回boxes+tracks结果"""
        if not self._initialized or self.model is None:
            raise RuntimeError("TPH算法未初始化")
        if not isinstance(frame, np.ndarray):
            raise ValueError("图像数据异常")
        result_dict = {"names": self.names}
        try:
            # 预处理
            img = letterbox(frame, self.imgsz, stride=self.stride, auto=True)[0]
            img = img.transpose((2, 0, 1))[::-1]  # HWC to CHW, BGR to RGB
            img = np.ascontiguousarray(img)

            # 转换为tensor
            img = torch.from_numpy(img).to(self.device)
            img = img.half() if self.half else img.float()
            img /= 255.0
            if img.ndimension() == 3:
                img = img.unsqueeze(0)

            # 推理
            pred = self.model(img, augment=False)[0]

            # NMS
            det = non_max_suppression(pred, self.conf, self.iou, max_det=self.max_det)

            # 处理检测结果
            if len(det) and len(det[0]):
                det = det[0]
                # 将坐标缩放回原图尺寸
                det[:, :4] = scale_coords(
                    img.shape[2:], det[:, :4], frame.shape
                ).round()
                # [x1, y1, x2, y2, confidence, class_id]
                boxes = det.cpu().numpy()
                result_dict["boxes"] = boxes

                result_dict["tracks"] = boxes

        except Exception:
            self.logger.error("TPH算法推理失败:", exc_info=True)

        return result_dict

    def cleanup(self):
        """清理资源"""
        if self.model:
            del self.model
            self.model = None

