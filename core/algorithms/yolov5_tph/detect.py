import logging
import os

import numpy as np
import torch

from models.experimental import attempt_load
from utils.augmentations import letterbox
from utils.general import check_img_size, non_max_suppression, scale_coords
from utils.torch_utils import select_device


class TPHAlgorithm:
    """YOLOv5 TPH 检测算法，集成跟踪功能"""
    alg_type = "yolov5"

    def __init__(self, alg_key: str, config: dict):
        self.logger = logging.getLogger(__name__)
        self.model = None
        self.names = {}
        self.stride = None
        self.imgsz = config.get("imgsz", [640, 640])
        self.device = config.get("device", "")
        self.half = config.get("half", True)
        self.conf = config.get("conf", 0.25)
        self.iou = config.get("iou", 0.45)
        self.max_det = config.get("max_det", 1000)
        self.weights = config.get("weights", f"models/{alg_key}.pt")
        self.alg_key = alg_key
        self.config = config
        self._initialized = False

    def initialize(self) -> bool:
        """初始化算法"""
        try:
            # 获取配置参数
            weights_path = self.weights
            if not os.path.isfile(weights_path):
                self.logger.error(f"找不到模型文件: {weights_path}")
                raise FileNotFoundError(f"找不到模型文件: {weights_path}")

            self.logger.info(f"初始化YOLOv5 TPH模型: {weights_path}")

            # 初始化设备
            self.device = select_device(self.device)
            self.half &= self.device.type != "cpu"

            # 加载模型
            self.model = self._load_model(weights_path)
            self.stride = int(self.model.stride.max())
            self.names = (
                self.model.module.names
                if hasattr(self.model, "module")
                else self.model.names
            )
            self.names = dict(enumerate(self.names))
            self.imgsz = check_img_size(self.imgsz, s=self.stride)

            # 初始化跟踪器
            track_config = self.config.get("track", {})

            # 预热模型
            if self.device.type != "cpu":
                self.model(
                    torch.zeros(1, 3, 640, 640)
                    .to(self.device)
                    .type_as(next(self.model.parameters()))
                )

            self.logger.info(
                f"YOLOv5 TPH算法初始化成功:{self.alg_key};{weights_path};{self.imgsz};"
                f"{self.conf};{self.iou};{self.device};"
                f"{self.half};{self.max_det};{self.names}"
            )

            self._initialized = True
            return True

        except Exception as e:
            self.logger.error("YOLOv5 TPH算法初始化失败:", exc_info=True)
            raise RuntimeError(f"YOLOv5 TPH算法初始化失败: {e}")

    def _load_model(self, weights_path):
        """加载模型"""
        w = str(weights_path)
        model = (
            torch.jit.load(w)
            if "torchscript" in w
            else attempt_load(weights_path, map_location=self.device)
        )
        if self.half:
            model.half()
        return model

    def process(self, frame):
        """处理帧并返回boxes+tracks结果"""
        if not self._initialized or self.model is None:
            raise RuntimeError("TPH算法未初始化")
        if not isinstance(frame, np.ndarray):
            raise ValueError("图像数据异常")
        result_dict = {"names": self.names}
        try:
            # 预处理
            img = letterbox(frame, self.imgsz, stride=self.stride, auto=True)[0]
            img = img.transpose((2, 0, 1))[::-1]  # HWC to CHW, BGR to RGB
            img = np.ascontiguousarray(img)

            # 转换为tensor
            img = torch.from_numpy(img).to(self.device)
            img = img.half() if self.half else img.float()
            img /= 255.0
            if img.ndimension() == 3:
                img = img.unsqueeze(0)

            # 推理
            pred = self.model(img, augment=False)[0]

            # NMS
            det = non_max_suppression(pred, self.conf, self.iou, max_det=self.max_det)

            # 处理检测结果
            if len(det) and len(det[0]):
                det = det[0]
                # 将坐标缩放回原图尺寸
                det[:, :4] = scale_coords(
                    img.shape[2:], det[:, :4], frame.shape
                ).round()
                # [x1, y1, x2, y2, confidence, class_id]
                boxes = det.cpu().numpy()
                result_dict["boxes"] = boxes

                result_dict["tracks"] = boxes

        except Exception:
            self.logger.error("TPH算法推理失败:", exc_info=True)

        return result_dict

    def cleanup(self):
        """清理资源"""
        if self.model:
            del self.model
            self.model = None

if __name__ == "__main__":
    import cv2
    import time
    from pathlib import Path

    def test_tph_algorithm():
        """测试TPH算法的完整功能"""
        print("开始测试TPH算法...")

        # 配置参数
        config = {
            "imgsz": [640, 640],
            "device": "",  # 自动选择设备
            "half": True,
            "conf": 0.25,
            "iou": 0.45,
            "max_det": 1000,
            "weights": "models/tph_yolov5x_best.pt"
        }

        # 检查模型文件是否存在
        model_path = Path(config["weights"])
        if not model_path.exists():
            print(f"错误: 模型文件不存在: {model_path}")
            print("请确保模型文件路径正确")
            return False

        # 初始化算法
        try:
            algorithm = TPHAlgorithm("tph_yolov5x_best", config)
            print("TPH算法实例创建成功")

            # 初始化算法
            success = algorithm.initialize()
            if not success:
                print("算法初始化失败")
                return False
            print("算法初始化成功")

        except Exception as e:
            print(f"算法初始化异常: {e}")
            return False

        # 测试1: 使用测试图像
        print("\n=== 测试1: 处理测试图像 ===")
        try:
            # 创建一个测试图像 (640x480, BGR格式)
            test_image = cv2.imread("test_image.jpg") if Path("test_image.jpg").exists() else None

            if test_image is None:
                # 如果没有测试图像，创建一个随机图像
                import numpy as np
                test_image = np.random.randint(0, 255, (480, 640, 3), dtype=np.uint8)
                print("使用随机生成的测试图像")
            else:
                print("使用本地测试图像")

            # 处理图像
            start_time = time.time()
            result = algorithm.process(test_image)
            process_time = time.time() - start_time

            print(f"图像处理耗时: {process_time:.3f}秒")
            print(f"检测结果: {result.keys()}")

            if "boxes" in result and result["boxes"] is not None:
                boxes = result["boxes"]
                print(f"检测到 {len(boxes)} 个目标")
                for i, box in enumerate(boxes[:5]):  # 只显示前5个
                    x1, y1, x2, y2, conf, cls = box
                    class_name = result["names"].get(int(cls), f"class_{int(cls)}")
                    print(f"  目标{i+1}: {class_name}, 置信度: {conf:.3f}, 坐标: ({x1:.0f},{y1:.0f},{x2:.0f},{y2:.0f})")
            else:
                print("未检测到目标")

        except Exception as e:
            print(f"图像处理测试失败: {e}")
            return False

        # 测试2: 使用视频文件（如果存在）
        print("\n=== 测试2: 处理视频文件 ===")
        video_paths = [
            "data/0.mp4",
            "test_video.mp4",
            "sample.mp4"
        ]

        video_path = None
        for path in video_paths:
            if Path(path).exists():
                video_path = path
                break

        if video_path:
            try:
                cap = cv2.VideoCapture(video_path)
                if not cap.isOpened():
                    print(f"无法打开视频文件: {video_path}")
                else:
                    print(f"处理视频文件: {video_path}")

                    frame_count = 0
                    total_time = 0

                    while frame_count < 10:  # 只处理前10帧
                        ret, frame = cap.read()
                        if not ret:
                            break

                        start_time = time.time()
                        result = algorithm.process(frame)
                        process_time = time.time() - start_time
                        total_time += process_time

                        frame_count += 1

                        if "boxes" in result and result["boxes"] is not None:
                            num_detections = len(result["boxes"])
                        else:
                            num_detections = 0

                        print(f"帧 {frame_count}: {num_detections} 个目标, 耗时: {process_time:.3f}秒")

                    cap.release()

                    if frame_count > 0:
                        avg_time = total_time / frame_count
                        fps = 1.0 / avg_time if avg_time > 0 else 0
                        print(f"平均处理时间: {avg_time:.3f}秒/帧")
                        print(f"理论FPS: {fps:.1f}")

            except Exception as e:
                print(f"视频处理测试失败: {e}")
        else:
            print("未找到测试视频文件，跳过视频测试")

        # 测试3: 性能基准测试
        print("\n=== 测试3: 性能基准测试 ===")
        try:
            # 创建标准测试图像
            test_image = np.random.randint(0, 255, (640, 640, 3), dtype=np.uint8)

            # 预热
            for _ in range(3):
                algorithm.process(test_image)

            # 基准测试
            num_iterations = 20
            times = []

            print(f"运行 {num_iterations} 次基准测试...")
            for i in range(num_iterations):
                start_time = time.time()
                result = algorithm.process(test_image)
                end_time = time.time()
                times.append(end_time - start_time)

                if (i + 1) % 5 == 0:
                    print(f"完成 {i + 1}/{num_iterations} 次测试")

            # 统计结果
            avg_time = np.mean(times)
            min_time = np.min(times)
            max_time = np.max(times)
            std_time = np.std(times)

            print(f"\n性能统计:")
            print(f"  平均时间: {avg_time:.3f}秒")
            print(f"  最短时间: {min_time:.3f}秒")
            print(f"  最长时间: {max_time:.3f}秒")
            print(f"  标准差: {std_time:.3f}秒")
            print(f"  理论FPS: {1.0/avg_time:.1f}")

        except Exception as e:
            print(f"性能测试失败: {e}")

        # 清理资源
        try:
            algorithm.cleanup()
            print("\n资源清理完成")
        except Exception as e:
            print(f"资源清理失败: {e}")

        print("\n测试完成!")
        return True

    def test_error_handling():
        """测试错误处理"""
        print("\n=== 错误处理测试 ===")

        # 测试无效配置
        try:
            config = {"weights": "nonexistent_model.pt"}
            algorithm = TPHAlgorithm("test", config)
            algorithm.initialize()
            print("错误: 应该抛出异常但没有")
        except Exception as e:
            print(f"正确捕获异常: {type(e).__name__}: {e}")

        # 测试未初始化的处理
        try:
            config = {"weights": "models/tph_yolov5x_best.pt"}
            algorithm = TPHAlgorithm("test", config)
            # 不调用initialize()
            test_image = np.random.randint(0, 255, (480, 640, 3), dtype=np.uint8)
            algorithm.process(test_image)
            print("错误: 应该抛出异常但没有")
        except Exception as e:
            print(f"正确捕获异常: {type(e).__name__}: {e}")

        # 测试无效输入
        try:
            config = {"weights": "models/tph_yolov5x_best.pt"}
            algorithm = TPHAlgorithm("test", config)
            algorithm.initialize()
            algorithm.process("invalid_input")  # 传入字符串而不是numpy数组
            print("错误: 应该抛出异常但没有")
        except Exception as e:
            print(f"正确捕获异常: {type(e).__name__}: {e}")
            algorithm.cleanup()

    # 运行测试
    print("=" * 60)
    print("TPH算法测试程序")
    print("=" * 60)

    # 主要功能测试
    success = test_tph_algorithm()

    # 错误处理测试
    test_error_handling()

    print("\n" + "=" * 60)
    if success:
        print("测试完成: 主要功能正常")
    else:
        print("测试完成: 发现问题，请检查配置和依赖")
    print("=" * 60)
