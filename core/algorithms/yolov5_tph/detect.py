import logging
import os

import numpy as np
import torch

from models.experimental import attempt_load
from utils.augmentations import letterbox
from utils.general import check_img_size, non_max_suppression, scale_coords
from utils.torch_utils import select_device


class TPHAlgorithm:
    """YOLOv5 TPH 检测算法，集成跟踪功能"""
    alg_type = "yolov5"

    def __init__(self, alg_key: str, config: dict):
        self.logger = logging.getLogger(__name__)
        self.model = None
        self.names = {}
        self.stride = None
        self.imgsz = config.get("imgsz", [640, 640])
        self.device = config.get("device", "")
        self.half = config.get("half", True)
        self.conf = config.get("conf", 0.25)
        self.iou = config.get("iou", 0.45)
        self.max_det = config.get("max_det", 1000)
        self.weights = config.get("weights", f"models/{alg_key}.pt")
        self.alg_key = alg_key
        self.config = config
        self._initialized = False

    def initialize(self) -> bool:
        """初始化算法"""
        try:
            # 获取配置参数
            weights_path = self.weights
            if not os.path.isfile(weights_path):
                self.logger.error(f"找不到模型文件: {weights_path}")
                raise FileNotFoundError(f"找不到模型文件: {weights_path}")

            self.logger.info(f"初始化YOLOv5 TPH模型: {weights_path}")

            # 初始化设备
            self.device = select_device(self.device)
            self.half &= self.device.type != "cpu"

            # 加载模型
            self.model = self._load_model(weights_path)
            self.stride = int(self.model.stride.max())
            self.names = (
                self.model.module.names
                if hasattr(self.model, "module")
                else self.model.names
            )
            self.names = dict(enumerate(self.names))
            self.imgsz = check_img_size(self.imgsz, s=self.stride)

            # 初始化跟踪器
            track_config = self.config.get("track", {})

            # 预热模型
            if self.device.type != "cpu":
                self.model(
                    torch.zeros(1, 3, 640, 640)
                    .to(self.device)
                    .type_as(next(self.model.parameters()))
                )

            self.logger.info(
                f"YOLOv5 TPH算法初始化成功:{self.alg_key};{weights_path};{self.imgsz};"
                f"{self.conf};{self.iou};{self.device};"
                f"{self.half};{self.max_det};{self.names}"
            )

            self._initialized = True
            return True

        except Exception as e:
            self.logger.error("YOLOv5 TPH算法初始化失败:", exc_info=True)
            raise RuntimeError(f"YOLOv5 TPH算法初始化失败: {e}")

    def _load_model(self, weights_path):
        """加载模型"""
        w = str(weights_path)
        model = (
            torch.jit.load(w)
            if "torchscript" in w
            else attempt_load(weights_path, map_location=self.device)
        )
        if self.half:
            model.half()
        return model

    def process(self, frame):
        """处理帧并返回boxes+tracks结果"""
        if not self._initialized or self.model is None:
            raise RuntimeError("TPH算法未初始化")
        if not isinstance(frame, np.ndarray):
            raise ValueError("图像数据异常")
        result_dict = {"names": self.names}
        try:
            # 预处理
            img = letterbox(frame, self.imgsz, stride=self.stride, auto=True)[0]
            img = img.transpose((2, 0, 1))[::-1]  # HWC to CHW, BGR to RGB
            img = np.ascontiguousarray(img)

            # 转换为tensor
            img = torch.from_numpy(img).to(self.device)
            img = img.half() if self.half else img.float()
            img /= 255.0
            if img.ndimension() == 3:
                img = img.unsqueeze(0)

            # 推理
            pred = self.model(img, augment=False)[0]

            # NMS
            det = non_max_suppression(pred, self.conf, self.iou, max_det=self.max_det)

            # 处理检测结果
            if len(det) and len(det[0]):
                det = det[0]
                # 将坐标缩放回原图尺寸
                det[:, :4] = scale_coords(
                    img.shape[2:], det[:, :4], frame.shape
                ).round()
                # [x1, y1, x2, y2, confidence, class_id]
                boxes = det.cpu().numpy()
                result_dict["boxes"] = boxes

                result_dict["tracks"] = boxes

        except Exception:
            self.logger.error("TPH算法推理失败:", exc_info=True)

        return result_dict

    def cleanup(self):
        """清理资源"""
        if self.model:
            del self.model
            self.model = None

if __name__ == "__main__":
    import cv2
    import time

    # 使用指定的模型和视频路径
    pt = r"C:\Z\y_streaming_3\models\tph_yolov5x_best.pt"
    video = r"C:\Z\y_streaming_3\data\0.mp4"

    print("=" * 60)
    print("TPH算法检测测试 - 实时显示")
    print("=" * 60)
    print(f"模型路径: {pt}")
    print(f"视频路径: {video}")
    print("\n🎮 控制说明:")
    print("  - 按 'q' 退出程序")
    print("  - 按 'p' 暂停/继续")
    print("  - 按 's' 保存当前帧")
    print("  - 关闭窗口也可退出")

    # 检查文件是否存在
    import os
    if not os.path.exists(pt):
        print(f"❌ 模型文件不存在: {pt}")
        exit(1)

    if not os.path.exists(video):
        print(f"❌ 视频文件不存在: {video}")
        exit(1)

    # 配置参数
    config = {
        "imgsz": [640, 640],
        "device": "",  # 自动选择设备
        "half": True,
        "conf": 0.25,
        "iou": 0.45,
        "max_det": 1000,
        "weights": pt
    }

    try:
        # 初始化算法
        print("\n🚀 初始化TPH算法...")
        algorithm = TPHAlgorithm("tph_yolov5x_best", config)
        algorithm.initialize()
        print("✅ 算法初始化成功")

        # 打开视频
        print(f"\n📹 打开视频文件: {video}")
        cap = cv2.VideoCapture(video)

        if not cap.isOpened():
            print("❌ 无法打开视频文件")
            exit(1)

        # 获取视频信息
        fps = cap.get(cv2.CAP_PROP_FPS)
        frame_count = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
        width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
        height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))

        print(f"视频信息: {width}x{height}, {fps:.1f}FPS, {frame_count}帧")

        # 处理视频帧
        print("\n🔍 开始检测...")
        print("-" * 60)
        frame_idx = 0
        total_time = 0
        total_detections = 0
        detection_details = []

        while True:
            ret, frame = cap.read()
            if not ret:
                break

            frame_idx += 1

            # 处理帧
            start_time = time.time()
            result = algorithm.process(frame)
            process_time = time.time() - start_time
            total_time += process_time

            # 统计检测结果和绘制检测框
            num_detections = 0
            frame_detections = []
            display_frame = frame.copy()  # 复制帧用于显示

            if "boxes" in result and result["boxes"] is not None:
                boxes = result["boxes"]
                num_detections = len(boxes)
                total_detections += num_detections

                # 绘制检测框并收集检测详情
                for i, box in enumerate(boxes):
                    x1, y1, x2, y2, conf, cls = box
                    x1, y1, x2, y2 = int(x1), int(y1), int(x2), int(y2)
                    class_name = result["names"].get(int(cls), f"class_{int(cls)}")

                    frame_detections.append({
                        'class': class_name,
                        'confidence': conf,
                        'bbox': [x1, y1, x2, y2]
                    })

                    # 绘制检测框
                    color = (0, 255, 0)  # 绿色框
                    thickness = 2
                    cv2.rectangle(display_frame, (x1, y1), (x2, y2), color, thickness)

                    # 绘制标签
                    label = f"{class_name}: {conf:.2f}"
                    label_size = cv2.getTextSize(label, cv2.FONT_HERSHEY_SIMPLEX, 0.6, 2)[0]

                    # 标签背景
                    cv2.rectangle(display_frame,
                                (x1, y1 - label_size[1] - 10),
                                (x1 + label_size[0], y1),
                                color, -1)

                    # 标签文字
                    cv2.putText(display_frame, label,
                              (x1, y1 - 5),
                              cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 0, 0), 2)

                # 显示前几帧的详细检测结果
                if frame_idx <= 10 and num_detections > 0:
                    print(f"\n📋 帧 {frame_idx} 详细检测结果:")
                    for i, detection in enumerate(frame_detections[:5]):  # 只显示前5个
                        x1, y1, x2, y2 = detection['bbox']
                        print(f"  🎯 目标{i+1}: {detection['class']}")
                        print(f"     置信度: {detection['confidence']:.3f}")
                        print(f"     位置: ({x1:.0f}, {y1:.0f}) -> ({x2:.0f}, {y2:.0f})")
                        print(f"     尺寸: {x2-x1:.0f} x {y2-y1:.0f}")
                    if len(frame_detections) > 5:
                        print(f"     ... 还有 {len(frame_detections)-5} 个目标")

            # 在图像上显示帧信息
            info_text = f"Frame: {frame_idx}/{frame_count} | Objects: {num_detections} | FPS: {1.0/process_time:.1f}"
            cv2.putText(display_frame, info_text, (10, 30),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.8, (255, 255, 255), 2)
            cv2.putText(display_frame, info_text, (10, 30),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.8, (0, 0, 0), 1)

            # 显示图像
            cv2.imshow('TPH Detection Results', display_frame)

            # 按键控制
            key = cv2.waitKey(1) & 0xFF
            if key == ord('q'):  # 按 'q' 退出
                print("\n⏹️  用户按 'q' 退出")
                break
            elif key == ord('p'):  # 按 'p' 暂停
                print("⏸️  暂停 - 按任意键继续...")
                cv2.waitKey(0)
            elif key == ord('s'):  # 按 's' 保存当前帧
                save_path = f"detection_frame_{frame_idx}.jpg"
                cv2.imwrite(save_path, display_frame)
                print(f"💾 保存帧到: {save_path}")

            detection_details.append({
                'frame': frame_idx,
                'detections': num_detections,
                'time': process_time,
                'details': frame_detections
            })

            # 每10帧显示一次进度
            if frame_idx % 10 == 0:
                avg_time = total_time / frame_idx
                current_fps = 1.0 / process_time if process_time > 0 else 0
                avg_fps = frame_idx / total_time if total_time > 0 else 0
                print(f"\n📊 进度报告 - 帧 {frame_idx}/{frame_count}:")
                print(f"   当前帧: {num_detections}个目标, 耗时: {process_time:.3f}s, FPS: {current_fps:.1f}")
                print(f"   平均: {total_detections/frame_idx:.1f}个目标/帧, 平均FPS: {avg_fps:.1f}")
                print("-" * 40)

            # 只处理前50帧进行演示
            if frame_idx >= 50:
                print("\n⏹️  演示模式：只处理前50帧")
                break

        cap.release()
        cv2.destroyAllWindows()  # 关闭所有OpenCV窗口

        # 显示详细统计结果
        print("\n" + "=" * 60)
        print("📊 TPH算法检测统计报告")
        print("=" * 60)

        print(f"🎬 视频信息:")
        print(f"   分辨率: {width} x {height}")
        print(f"   原始FPS: {fps:.1f}")
        print(f"   总帧数: {frame_count}")
        print(f"   处理帧数: {frame_idx}")

        print(f"\n🎯 检测统计:")
        print(f"   总检测数: {total_detections}")
        print(f"   平均每帧检测数: {total_detections/frame_idx:.1f}")
        print(f"   有目标的帧数: {sum(1 for d in detection_details if d['detections'] > 0)}")
        print(f"   检测率: {sum(1 for d in detection_details if d['detections'] > 0)/frame_idx*100:.1f}%")

        print(f"\n⚡ 性能统计:")
        print(f"   总处理时间: {total_time:.2f}秒")
        print(f"   平均处理时间: {total_time/frame_idx:.3f}秒/帧")
        print(f"   平均FPS: {frame_idx/total_time:.1f}")
        print(f"   最快处理时间: {min(d['time'] for d in detection_details):.3f}秒")
        print(f"   最慢处理时间: {max(d['time'] for d in detection_details):.3f}秒")

        # 按类别统计
        if total_detections > 0:
            class_counts = {}
            for detail in detection_details:
                for detection in detail['details']:
                    class_name = detection['class']
                    class_counts[class_name] = class_counts.get(class_name, 0) + 1

            print(f"\n🏷️  按类别统计:")
            for class_name, count in sorted(class_counts.items(), key=lambda x: x[1], reverse=True):
                percentage = count / total_detections * 100
                print(f"   {class_name}: {count} ({percentage:.1f}%)")

        # 显示检测密度最高的几帧
        top_frames = sorted(detection_details, key=lambda x: x['detections'], reverse=True)[:5]
        if top_frames[0]['detections'] > 0:
            print(f"\n🔥 检测密度最高的帧:")
            for i, frame_info in enumerate(top_frames):
                if frame_info['detections'] > 0:
                    print(f"   第{i+1}名: 帧{frame_info['frame']}, {frame_info['detections']}个目标, "
                          f"耗时{frame_info['time']:.3f}s")

        print("\n" + "=" * 60)

        # 清理资源
        algorithm.cleanup()
        print("✅ 测试完成，资源已清理")
        print("=" * 60)

    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
