# YOLOv5 🚀 by Ultralytics, GPL-3.0 license

# Parameters
nc: 80  # number of classes
depth_multiple: 1.0  # model depth multiple
width_multiple: 1.0  # layer channel multiple
anchors: 4
# - [10,13, 16,30, 33,23]  # P3/8
# - [30,61, 62,45, 59,119]  # P4/16
# - [116,90, 156,198, 373,326]  # P5/32

## YOLOv5 v6.0 backbone
#backbone:
#  # [from, number, module, args]
#  [[-1, 1, Conv, [64, 6, 2, 2]],  # 0-P1/2
#   [-1, 1, Conv, [128, 3, 2]],  # 1-P2/4
#   [-1, 3, C3, [128]],
#   [-1, 1, Conv, [256, 3, 2]],  # 3-P3/8
#   [-1, 6, C3, [256]],
#   [-1, 1, Conv, [512, 3, 2]],  # 5-P4/16
#   [-1, 9, C3, [512]],
#   [-1, 1, Conv, [1024, 3, 2]],  # 7-P5/32
#   [-1, 3, C3TR, [1024]],
#   [-1, 1, SPPF, [1024, 5]],  # 9
#  ]


# Swin-Transformer-Tiny backbone

backbone:
  # [from, number, module, args]
  # input [b, 1, 640, 640]
  [ [ -1, 1, PatchEmbed, [ 96, 4 ] ],  # 0 [b, 96, 160, 160]
    [ -1, 1, SwinStage, [ 96, 2, 3, 7 ] ],  # 1 [b, 96, 160, 160]
    [ -1, 1, <PERSON>Merging, [ 192 ] ],    # 2 [b, 192, 80, 80]
    [ -1, 1, SwinStage, [ 192, 2, 6, 7 ] ],  # 3 --F0-- [b, 192, 80, 80]
    [ -1, 1, PatchMerging, [ 384 ] ],   # 4 [b, 384, 40, 40]
    [ -1, 1, SwinStage, [ 384, 6, 12, 7 ] ], # 5 --F1-- [b, 384, 40, 40]
    [ -1, 1, PatchMerging, [ 768 ] ],   # 6 [b, 768, 20, 20]
    [ -1, 1, SwinStage, [ 768, 2, 24, 7 ] ], # 7 --F2-- [b, 768, 20, 20]
  ]

# YOLOv5 v6.0 head
#head:
#  [[-1, 1, Conv, [512, 1, 1]],
#   [-1, 1, nn.Upsample, [None, 2, 'nearest']],
#   [[-1, 6], 1, Concat, [1]],  # cat backbone P4
#   [-1, 3, C3, [512, False]],  # 11
#
#   [-1, 1, Conv, [256, 1, 1]],
#   [-1, 1, nn.Upsample, [None, 2, 'nearest']],
#   [[-1, 4], 1, Concat, [1]],  # cat backbone P3
#   [-1, 3, C3, [256, False]],  # 15 (P3/8-small)
#
#   [ -1, 1, Conv, [ 128, 1, 1 ] ],
#   [ -1, 1, nn.Upsample, [ None, 2, 'nearest' ] ],
#   [ [ -1, 2 ], 1, Concat, [ 1 ] ],  # cat backbone P2
#   [-1, 1, SPP, [128, [5, 9, 13]]],
#   [ -1, 3, C3, [ 128, False ] ],  #  (P2/4-xsmall)
#   [-1, 1, CBAM, [128]],           # 21
#
#   [ -1, 1, Conv, [ 128, 3, 2 ] ],
#   [ [ -1, 18, 4], 1, Concat, [ 1 ] ],  # cat head P3
#   [-1, 1, SPP, [256, [5, 9, 13]]],
#   [ -1, 3, C3, [ 256, False ] ],  # (P3/8-small)
#   [-1, 1, CBAM, [256]],          # 26
#
#   [-1, 1, Conv, [256, 3, 2]],
#   [[-1, 14, 6], 1, Concat, [1]],  # cat head P4
#   [-1, 1, SPP, [512, [3, 7, 11]]],
#   [-1, 3, C3, [512, False]],  #  (P4/16-medium)
#   [-1, 1, CBAM, [512]],       # 31
#
#   [-1, 1, Conv, [512, 3, 2]],
#   [[-1, 10], 1, Concat, [1]],  # cat head P5
#   [-1, 1, SPP, [1024, [3, 5, 7]]],
#   [-1, 3, C3TR, [1024, False]],  #  (P5/32-large)
#   [-1, 1, CBAM, [1024]],        # 36
#
#   [[21, 26, 31, 36], 1, Detect, [nc,anchors]],  # Detect(P2, P3, P4, P5)
#  ]

head:
  [ [ -1, 1, Conv, [ 512, 1, 1 ] ],
    [ -1, 1, nn.Upsample, [ None, 2, 'nearest' ] ],
    [ [ -1, 6 ], 1, Concat, [ 1 ] ],  # cat backbone P4
    [ -1, 3, C3, [ 512, False ] ],  # 11

    [ -1, 1, Conv, [ 256, 1, 1 ] ],
    [ -1, 1, nn.Upsample, [ None, 2, 'nearest' ] ],
    [ [ -1, 4 ], 1, Concat, [ 1 ] ],  # cat backbone P3
    [ -1, 3, C3, [ 256, False ] ],  # 15 (P3/8-small)

    [ -1, 1, Conv, [ 128, 1, 1 ] ],
    [ -1, 1, nn.Upsample, [ None, 2, 'nearest' ] ],
    [ [ -1, 2 ], 1, Concat, [ 1 ] ],  # cat backbone P2
    #   [-1, 1, SPP, [128, [5, 9, 13]]],
    [ -1, 3, C3, [ 128, False ] ],  #  (P2/4-xsmall)
    [ -1, 1, CBAM, [ 128 ] ],           # 20

    [ -1, 1, Conv, [ 128, 3, 2 ] ],
    [ [ -1, 18, 4 ], 1, Concat, [ 1 ] ],  # cat head P3
    #   [-1, 1, SPP, [256, [5, 9, 13]]],
    [ -1, 3, C3, [ 256, False ] ],  # (P3/8-small)
    [ -1, 1, CBAM, [ 256 ] ],          # 24

    [ -1, 1, Conv, [ 256, 3, 2 ] ],
    [ [ -1, 14, 6 ], 1, Concat, [ 1 ] ],  # cat head P4
    #   [-1, 1, SPP, [512, [3, 7, 11]]],
    [ -1, 3, C3, [ 512, False ] ],  #  (P4/16-medium)
    [ -1, 1, CBAM, [ 512 ] ],       # 28

    [ -1, 1, Conv, [ 512, 3, 2 ] ],
    [ [ -1, 10 ], 1, Concat, [ 1 ] ],  # cat head P5
    #   [-1, 1, SPP, [1024, [3, 5, 7]]],
    [ -1, 3, C3TR, [ 1024, False ] ],  #  (P5/32-large)
    [ -1, 1, CBAM, [ 1024 ] ],        # 32

    [ [ 20, 24, 28, 32 ], 1, Detect, [ nc,anchors ] ],  # Detect(P2, P3, P4, P5)
  ]


#head:
#  [[-1, 1, Conv, [512, 1, 1]],
#   [-1, 1, nn.Upsample, [None, 2, 'nearest']],
#   [[-1, 5], 1, Concat, [1]],  # cat backbone P4
#   [-1, 3, C3, [512, False]],  # 11
#
#   [-1, 1, Conv, [256, 1, 1]],
#   [-1, 1, nn.Upsample, [None, 2, 'nearest']],
#   [[-1, 3], 1, Concat, [1]],  # cat backbone P3
#   [-1, 3, C3, [256, False]],  # 15 (P3/8-small)
#
#   [-1, 1, Conv, [256, 3, 2]],
#   [[-1, 12], 1, Concat, [1]],  # cat head P4
#   [-1, 3, C3, [512, False]],  # 18 (P4/16-medium)
#
#   [-1, 1, Conv, [512, 3, 2]],
#   [[-1, 8], 1, Concat, [1]],  # cat head P5
#   [-1, 3, C3, [1024, False]],  # 21 (P5/32-large)
#
#   [[15, 18, 21], 1, Detect, [nc, anchors]],  # Detect(P3, P4, P5)
#   ]

#
#  1                -1  1    224694  models.swintransformer.SwinStage        [96, 96, 2, 3, 7]
#  2                -1  1     74496  models.swintransformer.PatchMerging     [96, 192]
#  3                -1  1    891756  models.swintransformer.SwinStage        [192, 192, 2, 6, 7]
#  4                -1  1    296448  models.swintransformer.PatchMerging     [192, 384]
#  5                -1  1  10658952  models.swintransformer.SwinStage        [384, 384, 6, 12, 7]
#  6                -1  1   1182720  models.swintransformer.PatchMerging     [384, 768]
#  7                -1  1  14183856  models.swintransformer.SwinStage        [768, 768, 2, 24, 7]
#  8                -1  1    394240  models.common.Conv                      [768, 512, 1, 1]
#  9                -1  1         0  torch.nn.modules.upsampling.Upsample    [None, 2, 'nearest']
# 10           [-1, 6]  1         0  models.common.Concat                    [1]
# 11                -1  3   2888704  models.common.C3                        [1280, 512, 3, False]
# 12                -1  1    131584  models.common.Conv                      [512, 256, 1, 1]
# 13                -1  1         0  torch.nn.modules.upsampling.Upsample    [None, 2, 'nearest']
# 14           [-1, 4]  1         0  models.common.Concat                    [1]
# 15                -1  3    723456  models.common.C3                        [640, 256, 3, False]
# 16                -1  1     33024  models.common.Conv                      [256, 128, 1, 1]
# 17                -1  1         0  torch.nn.modules.upsampling.Upsample    [None, 2, 'nearest']
# 18           [-1, 2]  1         0  models.common.Concat                    [1]
# 19                -1  1    133696  models.common.SPP                       [320, 128, [5, 9, 13]]
# 20                -1  3    156928  models.common.C3                        [128, 128, 3, False]
# 21                -1  1      2283  models.common.CBAM                      [128, 128]
# 22                -1  1    147712  models.common.Conv                      [128, 128, 3, 2]
# 23       [-1, 18, 4]  1         0  models.common.Concat                    [1]
# 24                -1  1    773440  models.common.SPP                       [832, 256, [5, 9, 13]]
# 25                -1  3    625152  models.common.C3                        [256, 256, 3, False]
# 26                -1  1      8563  models.common.CBAM                      [256, 256]
# 27                -1  1    590336  models.common.Conv                      [256, 256, 3, 2]
# 28       [-1, 14, 6]  1         0  models.common.Concat                    [1]
# 29                -1  1   3091072  models.common.SPP                       [1664, 512, [3, 7, 11]]
# 30                -1  3   2495488  models.common.C3                        [512, 512, 3, False]
# 31                -1  1     33411  models.common.CBAM                      [512, 512]
# 32                -1  1   2360320  models.common.Conv                      [512, 512, 3, 2]
# 33          [-1, 10]  1         0  models.common.Concat                    [1]
# 34                -1  1   5279488  models.common.SPP                       [1792, 1024, [3, 5, 7]]
# 35                -1  3  14172672  models.common.C3TR                      [1024, 1024, 3, False]
# 36                -1  1    132259  models.common.CBAM                      [1024, 1024]
# 37      [26, 31, 36]  1    610300  Detect                                  [80, [[0, 1, 2, 3, 4, 5, 6, 7], [0, 1, 2, 3, 4, 5, 6, 7], [0, 1, 2, 3, 4, 5, 6, 7]], [256, 512, 1024]]
