# YOLOv5 🚀 by Ultralytics, GPL-3.0 license

# Parameters
nc: 80  # number of classes
depth_multiple: 1.0  # model depth multiple
width_multiple: 1.0  # layer channel multiple
anchors: 4
# - [10,13, 16,30, 33,23]  # P3/8
# - [30,61, 62,45, 59,119]  # P4/16
# - [116,90, 156,198, 373,326]  # P5/32

# YOLOv5 v6.0 backbone
backbone:
  # [from, number, module, args]
  [ [ -1, 1, Conv, [ 64, 6, 2, 2 ] ],  # 0-P1/2
    [ -1, 1, Conv, [ 128, 3, 2 ] ],  # 1-P2/4
    [ -1, 3, C3, [ 128 ] ],
    [ -1, 1, Conv, [ 256, 3, 2 ] ],  # 3-P3/8
    [ -1, 6, C3, [ 256 ] ],
    [ -1, 1, Conv, [ 512, 3, 2 ] ],  # 5-P4/16
    [ -1, 9, C3, [ 512 ] ],
    [ -1, 1, Conv, [ 1024, 3, 2 ] ],  # 7-P5/32
    [ -1, 3, C3TR, [ 1024 ] ],
    [ -1, 1, <PERSON><PERSON><PERSON>, [ 1024, 5 ] ],  # 9
  ]

# YOLOv5 v6.0 head
head:
  [ [ -1, 1, Conv, [ 512, 1, 1 ] ],
    [ -1, 1, nn.Upsample, [ None, 2, 'nearest' ] ],
    [ [ -1, 6 ], 1, Concat, [ 1 ] ],  # cat backbone P4
    [ -1, 3, C3, [ 512, False ] ],  # 13

    [ -1, 1, Conv, [ 256, 1, 1 ] ],
    [ -1, 1, nn.Upsample, [ None, 2, 'nearest' ] ],
    [ [ -1, 4 ], 1, Concat, [ 1 ] ],  # cat backbone P3
    [ -1, 3, C3, [ 256, False ] ],  # 17 (P3/8-small)

    [ -1, 1, Conv, [ 128, 1, 1 ] ],
    [ -1, 1, nn.Upsample, [ None, 2, 'nearest' ] ],
    [ [ -1, 2 ], 1, Concat, [ 1 ] ],  # cat backbone P2
    [ -1, 1, SPP, [ 128, [ 5, 9, 13 ] ] ],
    [ -1, 3, C3, [ 128, False ] ],  #  (P2/4-xsmall)
    [ -1, 1, CBAM, [ 128 ] ],           # 23

    [ -1, 1, Conv, [ 128, 3, 2 ] ],
    [ [ -1, 18, 4 ], 1, Concat, [ 1 ] ],  # cat head P3
    [ -1, 1, SPP, [ 256, [ 5, 9, 13 ] ] ],
    [ -1, 3, C3, [ 256, False ] ],  # (P3/8-small)
    [ -1, 1, CBAM, [ 256 ] ],          # 28

    [ -1, 1, Conv, [ 256, 3, 2 ] ],
    [ [ -1, 14, 6 ], 1, Concat, [ 1 ] ],  # cat head P4
    [ -1, 1, SPP, [ 512, [ 3, 7, 11 ] ] ],
    [ -1, 3, C3, [ 512, False ] ],  #  (P4/16-medium)
    [ -1, 1, CBAM, [ 512 ] ],       # 33

    [ -1, 1, Conv, [ 512, 3, 2 ] ],
    [ [ -1, 10 ], 1, Concat, [ 1 ] ],  # cat head P5
    [ -1, 1, SPP, [ 1024, [ 3, 5, 7 ] ] ],
    [ -1, 3, C3TR, [ 1024, False ] ],  #  (P5/32-large)
    [ -1, 1, CBAM, [ 1024 ] ],        # 38

    [ [ 23, 28, 33, 38 ], 1, Detect, [ nc,anchors ] ],  # Detect(P2, P3, P4, P5)
  ]

#                 from  n    params  module                                  arguments
#  0                -1  1      7040  models.common.Conv                      [3, 64, 6, 2, 2]
#  1                -1  1     73984  models.common.Conv                      [64, 128, 3, 2]
#  2                -1  3    156928  models.common.C3                        [128, 128, 3]
#  3                -1  1    295424  models.common.Conv                      [128, 256, 3, 2]
#  4                -1  6   1118208  models.common.C3                        [256, 256, 6]
#  5                -1  1   1180672  models.common.Conv                      [256, 512, 3, 2]
#  6                -1  9   6433792  models.common.C3                        [512, 512, 9]
#  7                -1  1   4720640  models.common.Conv                      [512, 1024, 3, 2]
#  8                -1  3  14172672  models.common.C3TR                      [1024, 1024, 3]
#  9                -1  1   2624512  models.common.SPPF                      [1024, 1024, 5]
# 10                -1  1    525312  models.common.Conv                      [1024, 512, 1, 1]
# 11                -1  1         0  torch.nn.modules.upsampling.Upsample    [None, 2, 'nearest']
# 12           [-1, 6]  1         0  models.common.Concat                    [1]
# 13                -1  3   2757632  models.common.C3                        [1024, 512, 3, False]
# 14                -1  1    131584  models.common.Conv                      [512, 256, 1, 1]
# 15                -1  1         0  torch.nn.modules.upsampling.Upsample    [None, 2, 'nearest']
# 16           [-1, 4]  1         0  models.common.Concat                    [1]
# 17                -1  3    690688  models.common.C3                        [512, 256, 3, False]
# 18                -1  1     33024  models.common.Conv                      [256, 128, 1, 1]
# 19                -1  1         0  torch.nn.modules.upsampling.Upsample    [None, 2, 'nearest']
# 20           [-1, 2]  1         0  models.common.Concat                    [1]
# 21                -1  1     98816  models.common.SPP                       [256, 128, [5, 9, 13]]
# 22                -1  3    156928  models.common.C3                        [128, 128, 3, False]
# 23                -1  1      2283  models.common.CBAM                      [128, 128]
# 24                -1  1    147712  models.common.Conv                      [128, 128, 3, 2]
# 25       [-1, 18, 4]  1         0  models.common.Concat                    [1]
# 26                -1  1    394240  models.common.SPP                       [512, 256, [5, 9, 13]]
# 27                -1  3    625152  models.common.C3                        [256, 256, 3, False]
# 28                -1  1      8563  models.common.CBAM                      [256, 256]
# 29                -1  1    590336  models.common.Conv                      [256, 256, 3, 2]
# 30       [-1, 14, 6]  1         0  models.common.Concat                    [1]
# 31                -1  1   1574912  models.common.SPP                       [1024, 512, [3, 7, 11]]
# 32                -1  3   2495488  models.common.C3                        [512, 512, 3, False]
# 33                -1  1     33411  models.common.CBAM                      [512, 512]
# 34                -1  1   2360320  models.common.Conv                      [512, 512, 3, 2]
# 35          [-1, 10]  1         0  models.common.Concat                    [1]
# 36                -1  1   2624512  models.common.SPP                       [1024, 1024, [3, 5, 7]]
# 37                -1  3  14172672  models.common.C3TR                      [1024, 1024, 3, False]
# 38                -1  1    132259  models.common.CBAM                      [1024, 1024]
# 39  [23, 28, 33, 38]  1    654160  Detect                                  [80, [[0, 1, 2, 3, 4, 5, 6, 7], [0, 1, 2, 3, 4, 5, 6, 7], [0, 1, 2, 3, 4, 5, 6, 7], [0, 1, 2, 3, 4, 5, 6, 7]], [128, 256, 512, 1024]]

#Model Summary: 684 layers, 60993876 parameters, 60993876 gradients, 150.0 GFLOPs