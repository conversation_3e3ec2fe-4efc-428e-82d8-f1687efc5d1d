# YOLOv5 🚀 by Ultralytics, GPL-3.0 license

# Parameters
nc: 80  # number of classes
depth_multiple: 1.33  # model depth multiple
width_multiple: 1.25  # layer channel multiple
anchors: 4
#  - [10,13, 16,30, 33,23]  # P3/8
#  - [30,61, 62,45, 59,119]  # P4/16
#  - [116,90, 156,198, 373,326]  # P5/32

# YOLOv5 v6.0 backbone
backbone:
  # [from, number, module, args]
  [ [ -1, 1, Conv, [ 64, 6, 2, 2 ] ],  # 0-P1/2
    [ -1, 1, Conv, [ 128, 3, 2 ] ],  # 1-P2/4
    [ -1, 3, C3, [ 128 ] ],
    [ -1, 1, Conv, [ 256, 3, 2 ] ],  # 3-P3/8
    [ -1, 6, C3, [ 256 ] ],
    [ -1, 1, Conv, [ 512, 3, 2 ] ],  # 5-P4/16
    [ -1, 9, C3, [ 512 ] ],
    [ -1, 1, Conv, [ 1024, 3, 2 ] ],  # 7-P5/32
    [ -1, 3, C3, [ 1024 ] ],
    [ -1, 1, <PERSON><PERSON><PERSON>, [ 1024, 5 ] ],  # 9
  ]

## YOLOv5 v6.0 head
#head:
#  [[-1, 1, Conv, [512, 1, 1]],
#   [-1, 1, nn.Upsample, [None, 2, 'nearest']],
#   [[-1, 6], 1, Concat, [1]],  # cat backbone P4
#   [-1, 3, C3, [512, False]],  # 13
#
#   [-1, 1, Conv, [256, 1, 1]],
#   [-1, 1, nn.Upsample, [None, 2, 'nearest']],
#   [[-1, 4], 1, Concat, [1]],  # cat backbone P3
#   [-1, 3, C3, [256, False]],  # 17 (P3/8-small)
#
#   [-1, 1, Conv, [256, 3, 2]],
#   [[-1, 14], 1, Concat, [1]],  # cat head P4
#   [-1, 3, C3, [512, False]],  # 20 (P4/16-medium)
#
#   [-1, 1, Conv, [512, 3, 2]],
#   [[-1, 10], 1, Concat, [1]],  # cat head P5
#   [-1, 3, C3, [1024, False]],  # 23 (P5/32-large)
#
#   [[17, 20, 23], 1, Detect, [nc, anchors]],  # Detect(P3, P4, P5)
#  ]

# YOLOv5 v6.0 head
head:
  [ [ -1, 1, Conv, [ 512, 1, 1 ] ],
    [ -1, 1, nn.Upsample, [ None, 2, 'nearest' ] ],
    [ [ -1, 6 ], 1, Concat, [ 1 ] ],  # cat backbone P4
    [ -1, 3, C3, [ 512, False ] ],  # 13

    [ -1, 1, Conv, [ 256, 1, 1 ] ],
    [ -1, 1, nn.Upsample, [ None, 2, 'nearest' ] ],
    [ [ -1, 4 ], 1, Concat, [ 1 ] ],  # cat backbone P3
    [ -1, 3, C3, [ 256, False ] ],  # 17 (P3/8-small)

    [ -1, 1, Conv, [ 128, 1, 1 ] ],
    [ -1, 1, nn.Upsample, [ None, 2, 'nearest' ] ],
    [ [ -1, 2 ], 1, Concat, [ 1 ] ],  # cat backbone P2
    [ -1, 1, SPP, [ 128, [ 5, 9, 13 ] ] ],
    [ -1, 3, C3, [ 128, False ] ],  #  (P2/4-xsmall)
    [ -1, 1, CBAM, [ 128 ] ],           # 23

    [ -1, 1, Conv, [ 128, 3, 2 ] ],
    [ [ -1, 18, 4 ], 1, Concat, [ 1 ] ],  # cat head P3
    [ -1, 1, SPP, [ 256, [ 5, 9, 13 ] ] ],
    [ -1, 3, C3, [ 256, False ] ],  # (P3/8-small)
    [ -1, 1, CBAM, [ 256 ] ],          # 28

    [ -1, 1, Conv, [ 256, 3, 2 ] ],
    [ [ -1, 14, 6 ], 1, Concat, [ 1 ] ],  # cat head P4
    [ -1, 1, SPP, [ 512, [ 3, 7, 11 ] ] ],
    [ -1, 3, C3, [ 512, False ] ],  #  (P4/16-medium)
    [ -1, 1, CBAM, [ 512 ] ],       # 33

    [ -1, 1, Conv, [ 512, 3, 2 ] ],
    [ [ -1, 10 ], 1, Concat, [ 1 ] ],  # cat head P5
    [ -1, 1, SPP, [ 1024, [ 3, 5, 7 ] ] ],
    [ -1, 3, C3TR, [ 1024, False ] ],  #  (P5/32-large)
    [ -1, 1, CBAM, [ 1024 ] ],        # 38

    [ [ 23, 28, 33, 38 ], 1, Detect, [ nc,anchors ] ],  # Detect(P2, P3, P4, P5)
  ]


#                 from  n    params  module                                  arguments
#  0                -1  1      8800  models.common.Conv                      [3, 80, 6, 2, 2]
#  1                -1  1    115520  models.common.Conv                      [80, 160, 3, 2]
#  2                -1  4    309120  models.common.C3                        [160, 160, 4]
#  3                -1  1    461440  models.common.Conv                      [160, 320, 3, 2]
#  4                -1  8   2259200  models.common.C3                        [320, 320, 8]
#  5                -1  1   1844480  models.common.Conv                      [320, 640, 3, 2]
#  6                -1 12  13125120  models.common.C3                        [640, 640, 12]
#  7                -1  1   7375360  models.common.Conv                      [640, 1280, 3, 2]
#  8                -1  4  19676160  models.common.C3                        [1280, 1280, 4]
#  9                -1  1   4099840  models.common.SPPF                      [1280, 1280, 5]
# 10                -1  1    820480  models.common.Conv                      [1280, 640, 1, 1]
# 11                -1  1         0  torch.nn.modules.upsampling.Upsample    [None, 2, 'nearest']
# 12           [-1, 6]  1         0  models.common.Concat                    [1]
# 13                -1  4   5332480  models.common.C3                        [1280, 640, 4, False]
# 14                -1  1    205440  models.common.Conv                      [640, 320, 1, 1]
# 15                -1  1         0  torch.nn.modules.upsampling.Upsample    [None, 2, 'nearest']
# 16           [-1, 4]  1         0  models.common.Concat                    [1]
# 17                -1  4   1335040  models.common.C3                        [640, 320, 4, False]
# 18                -1  1     51520  models.common.Conv                      [320, 160, 1, 1]
# 19                -1  1         0  torch.nn.modules.upsampling.Upsample    [None, 2, 'nearest']
# 20           [-1, 2]  1         0  models.common.Concat                    [1]
# 21                -1  1    154240  models.common.SPP                       [320, 160, [5, 9, 13]]
# 22                -1  4    309120  models.common.C3                        [160, 160, 4, False]
# 23                -1  1      3469  models.common.CBAM                      [160, 160]
# 24                -1  1    230720  models.common.Conv                      [160, 160, 3, 2]
# 25       [-1, 18, 4]  1         0  models.common.Concat                    [1]
# 26                -1  1    615680  models.common.SPP                       [640, 320, [5, 9, 13]]
# 27                -1  4   1232640  models.common.C3                        [320, 320, 4, False]
# 28                -1  1     13239  models.common.CBAM                      [320, 320]
# 29                -1  1    922240  models.common.Conv                      [320, 320, 3, 2]
# 30       [-1, 14, 6]  1         0  models.common.Concat                    [1]
# 31                -1  1   2460160  models.common.SPP                       [1280, 640, [3, 7, 11]]
# 32                -1  4   4922880  models.common.C3                        [640, 640, 4, False]
# 33                -1  1     51979  models.common.CBAM                      [640, 640]
# 34                -1  1   3687680  models.common.Conv                      [640, 640, 3, 2]
# 35          [-1, 10]  1         0  models.common.Concat                    [1]
# 36                -1  1   4099840  models.common.SPP                       [1280, 1280, [3, 5, 7]]
# 37                -1  4  28288640  models.common.C3TR                      [1280, 1280, 4, False]
# 38                -1  1    206259  models.common.CBAM                      [1280, 1280]
# 39  [23, 28, 33, 38]  1    817360  Detect                                  [80, [[0, 1, 2, 3, 4, 5, 6, 7], [0, 1, 2, 3, 4, 5, 6, 7], [0, 1, 2, 3, 4, 5, 6, 7], [0, 1, 2, 3, 4, 5, 6, 7]], [160, 320, 640, 1280]]
#Model Summary: 793 layers, 105036146 parameters, 105036146 gradients, 274.2 GFLOPs