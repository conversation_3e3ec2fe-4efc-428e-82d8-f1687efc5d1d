# YOLOv5 🚀 by Ultralytics, GPL-3.0 license

# Parameters
nc: 80  # number of classes
depth_multiple: 1.0  # model depth multiple
width_multiple: 1.0  # layer channel multiple
anchors: 4
# - [10,13, 16,30, 33,23]  # P3/8
# - [30,61, 62,45, 59,119]  # P4/16
# - [116,90, 156,198, 373,326]  # P5/32


# Swin-Transformer-Tiny backbone

backbone:
  # [from, number, module, args]
  # input [b, 1, 640, 640]
  [ [ -1, 1, <PERSON><PERSON><PERSON><PERSON>, [ 96, 4 ] ],  # 0 [b, 96, 160, 160]
    [ -1, 1, Swin<PERSON><PERSON>, [ 96, 2, 3, 7 ] ],  # 1 [b, 96, 160, 160]
    [ -1, 1, <PERSON><PERSON><PERSON>, [ 192 ] ],    # 2 [b, 192, 80, 80]
    [ -1, 1, Swin<PERSON><PERSON>, [ 192, 2, 6, 7 ] ],  # 3 --F0-- [b, 192, 80, 80]
    [ -1, 1, Patch<PERSON><PERSON>, [ 384 ] ],   # 4 [b, 384, 40, 40]
    [ -1, 1, <PERSON>win<PERSON><PERSON>, [ 384, 6, 12, 7 ] ], # 5 --F1-- [b, 384, 40, 40]
    [ -1, 1, <PERSON><PERSON><PERSON>, [ 768 ] ],   # 6 [b, 768, 20, 20]
    [ -1, 1, SwinStage, [ 768, 2, 24, 7 ] ], # 7 --F2-- [b, 768, 20, 20]
  ]

# YOLOv5 v6.0 head

head:
  [ [ -1, 1, Conv, [ 512, 1, 1 ] ],
    [ -1, 1, nn.Upsample, [ None, 2, 'nearest' ] ],
    [ [ -1, 5 ], 1, Concat, [ 1 ] ],  # cat backbone P4
    [ -1, 3, C3, [ 512, False ] ],  # 11

    [ -1, 1, Conv, [ 256, 1, 1 ] ],
    [ -1, 1, nn.Upsample, [ None, 2, 'nearest' ] ],
    [ [ -1, 3 ], 1, Concat, [ 1 ] ],  # cat backbone P3
    [ -1, 3, C3, [ 256, False ] ],  # 15 (P3/8-small)

    [ -1, 1, Conv, [ 256, 3, 2 ] ],
    [ [ -1, 12 ], 1, Concat, [ 1 ] ],  # cat head P4
    [ -1, 3, C3, [ 512, False ] ],  # 18 (P4/16-medium)

    [ -1, 1, Conv, [ 512, 3, 2 ] ],
    [ [ -1, 8 ], 1, Concat, [ 1 ] ],  # cat head P5
    [ -1, 3, C3, [ 1024, False ] ],  # 21 (P5/32-large)

    [ [ 15, 18, 21 ], 1, Detect, [ nc, anchors ] ],  # Detect(P3, P4, P5)
  ]


#  1                -1  1    224694  models.swintransformer.SwinStage        [96, 96, 2, 3, 7]
#  2                -1  1     74496  models.swintransformer.PatchMerging     [96, 192]
#  3                -1  1    891756  models.swintransformer.SwinStage        [192, 192, 2, 6, 7]
#  4                -1  1    296448  models.swintransformer.PatchMerging     [192, 384]
#  5                -1  1  10658952  models.swintransformer.SwinStage        [384, 384, 6, 12, 7]
#  6                -1  1   1182720  models.swintransformer.PatchMerging     [384, 768]
#  7                -1  1  14183856  models.swintransformer.SwinStage        [768, 768, 2, 24, 7]
#  8                -1  1    394240  models.common.Conv                      [768, 512, 1, 1]
#  9                -1  1         0  torch.nn.modules.upsampling.Upsample    [None, 2, 'nearest']
# 10           [-1, 5]  1         0  models.common.Concat                    [1]
# 11                -1  3   2692096  models.common.C3                        [896, 512, 3, False]
# 12                -1  1    131584  models.common.Conv                      [512, 256, 1, 1]
# 13                -1  1         0  torch.nn.modules.upsampling.Upsample    [None, 2, 'nearest']
# 14           [-1, 3]  1         0  models.common.Concat                    [1]
# 15                -1  3    674304  models.common.C3                        [448, 256, 3, False]
# 16                -1  1    590336  models.common.Conv                      [256, 256, 3, 2]
# 17          [-1, 12]  1         0  models.common.Concat                    [1]
# 18                -1  3   2495488  models.common.C3                        [512, 512, 3, False]
# 19                -1  1   2360320  models.common.Conv                      [512, 512, 3, 2]
# 20           [-1, 8]  1         0  models.common.Concat                    [1]
# 21                -1  3   9971712  models.common.C3                        [1024, 1024, 3, False]
# 22      [15, 18, 21]  1    610300  Detect                                  [80, [[0, 1, 2, 3, 4, 5, 6, 7], [0, 1, 2, 3, 4, 5, 6, 7], [0, 1, 2, 3, 4, 5, 6, 7]], [256, 512, 1024]]
#Model Summary: 405 layers, 47438006 parameters, 47438006 gradients, 436.1 GFLOPs
